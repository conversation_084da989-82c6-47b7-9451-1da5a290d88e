<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prevost | DMS (Dealer Management System)</title>

    <!-- Material-UI CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mui/material@5.14.20/umd/material-ui.min.css" rel="stylesheet">

    <!-- Dashboard CSS -->
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <div id="root">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="header-left">
                    <img src="Prevost.png" alt="Prevost Logo" class="header-logo">
                    <div class="header-title">
                        <h1>DMS</h1>
                    </div>
                </div>

                <!-- Header Center - Search -->
                <div class="header-center">
                    <div class="search-container">
                        <span class="material-icons search-icon">search</span>
                        <input type="text"
                               id="globalSearch"
                               class="search-input"
                               placeholder="Search vehicles, customers, reports..."
                               aria-label="Global search">
                        <button class="search-clear" id="searchClear" aria-label="Clear search">
                            <span class="material-icons">close</span>
                        </button>
                    </div>
                </div>

                <div class="header-right">
                    <!-- Branch Selection -->
                    <div class="header-dropdown branch-selector">
                        <button class="dropdown-trigger" id="branchSelector" aria-haspopup="true" aria-expanded="false">
                            <span class="material-icons">business</span>
                            <span class="dropdown-text">Main Branch</span>
                            <span class="material-icons dropdown-arrow">expand_more</span>
                        </button>
                        <div class="dropdown-menu" id="branchMenu" role="menu">
                            <div class="dropdown-item active" role="menuitem" data-branch="main">
                                <span class="material-icons">business</span>
                                Main Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="north">
                                <span class="material-icons">business</span>
                                North Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="south">
                                <span class="material-icons">business</span>
                                South Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="east">
                                <span class="material-icons">business</span>
                                East Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="west">
                                <span class="material-icons">business</span>
                                West Branch
                            </div>
                        </div>
                    </div>

                    <!-- Release Notes -->
                    <button class="header-button" id="releaseNotesButton" aria-label="View release notes">
                        <span class="material-icons">new_releases</span>
                        <span class="button-text">Release Notes</span>
                        <span class="notification-badge" id="releaseNotesBadge">2</span>
                    </button>

                    <!-- Dashboard Customization -->
                    <button class="header-button" id="customizeButton" aria-label="Customize dashboard">
                        <span class="material-icons">tune</span>
                        <span class="button-text">Customize</span>
                    </button>

                    <!-- Month-end Process -->
                    <button class="header-button month-end-button" id="monthEndButton" aria-label="Month-end process">
                        <span class="material-icons">event_note</span>
                        <span class="button-text">Month-End</span>
                    </button>

                    <!-- Admin Profile Settings -->
                    <div class="header-dropdown admin-dropdown">
                        <button class="dropdown-trigger user-profile" id="adminProfileButton" aria-haspopup="true" aria-expanded="false">
                            <div class="user-info">
                                <span class="user-name" id="userName">Administrator</span>
                                <span class="user-role" id="userRole">Admin</span>
                            </div>
                            <div class="user-avatar">
                                <span class="material-icons">account_circle</span>
                            </div>
                            <span class="material-icons dropdown-arrow">expand_more</span>
                        </button>
                        <div class="dropdown-menu admin-menu" id="adminMenu" role="menu">
                            <div class="dropdown-item" role="menuitem" data-action="profile">
                                <span class="material-icons">person</span>
                                Profile Settings
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="preferences">
                                <span class="material-icons">settings</span>
                                Preferences
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="security">
                                <span class="material-icons">security</span>
                                Security Settings
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="notifications">
                                <span class="material-icons">notifications</span>
                                Notifications
                            </div>
                            <div class="dropdown-separator"></div>
                            <div class="dropdown-item" role="menuitem" data-action="help">
                                <span class="material-icons">help</span>
                                Help & Support
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="about">
                                <span class="material-icons">info</span>
                                About DMS
                            </div>
                            <div class="dropdown-separator"></div>
                            <div class="dropdown-item logout-item" role="menuitem" data-action="logout">
                                <span class="material-icons">logout</span>
                                Logout
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Navigation Sidebar -->
            <nav class="dashboard-nav">
                <div class="nav-section">
                    <h3 class="nav-title">Dashboard</h3>
                    <ul class="nav-list">
                        <li class="nav-item active">
                            <a href="#overview" class="nav-link">
                                <span class="material-icons">dashboard</span>
                                Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#inventory" class="nav-link">
                                <span class="material-icons">directions_bus</span>
                                Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#sales" class="nav-link">
                                <span class="material-icons">trending_up</span>
                                Sales
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#customers" class="nav-link">
                                <span class="material-icons">people</span>
                                Customers
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3 class="nav-title">Management</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#branches" class="nav-link">
                                <span class="material-icons">business</span>
                                Branches
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#users" class="nav-link">
                                <span class="material-icons">admin_panel_settings</span>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reports" class="nav-link">
                                <span class="material-icons">assessment</span>
                                Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#settings" class="nav-link">
                                <span class="material-icons">settings</span>
                                Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Content Area -->
            <div class="dashboard-content">
                <div class="content-header">
                    <h2 class="page-title">Dashboard Overview</h2>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span class="material-icons">chevron_right</span>
                        <span>Dashboard</span>
                    </div>
                </div>

                <!-- Dashboard Cards -->
                <div class="dashboard-cards">
                    <div class="dashboard-card">
                        <div class="card-icon">
                            <span class="material-icons">directions_bus</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Total Vehicles</h3>
                            <p class="card-value">247</p>
                            <p class="card-change positive">+12 this month</p>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">
                            <span class="material-icons">trending_up</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Monthly Sales</h3>
                            <p class="card-value">$2.4M</p>
                            <p class="card-change positive">+8.5% vs last month</p>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">
                            <span class="material-icons">people</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Active Customers</h3>
                            <p class="card-value">1,847</p>
                            <p class="card-change positive">+23 new this week</p>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <div class="card-icon">
                            <span class="material-icons">business</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Active Branches</h3>
                            <p class="card-value">5</p>
                            <p class="card-change neutral">All operational</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-section">
                    <h3 class="section-title">Recent Activity</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">add_circle</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New vehicle added to inventory: Prevost H3-45</p>
                                <p class="activity-time">2 hours ago</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">person_add</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New customer registered: Transit Solutions Inc.</p>
                                <p class="activity-time">4 hours ago</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">sell</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">Sale completed: Prevost X3-45 to Metro Transit</p>
                                <p class="activity-time">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="dashboard-footer">
            <div class="footer-content">
                <p>&copy; 2025 Prevost. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#" class="footer-link">Privacy Policy</a>
                    <span class="footer-separator">|</span>
                    <a href="#" class="footer-link">Terms of Service</a>
                    <span class="footer-separator">|</span>
                    <a href="#" class="footer-link">Support</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Release Notes Modal -->
    <div class="modal-overlay" id="releaseNotesModal" role="dialog" aria-labelledby="releaseNotesTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="releaseNotesTitle">Release Notes</h2>
                <button class="modal-close" id="closeReleaseNotes" aria-label="Close release notes">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="release-notes-tabs">
                    <button class="tab-button active" data-tab="current">Current Release</button>
                    <button class="tab-button" data-tab="previous">Previous Releases</button>
                </div>
                <div class="tab-content active" id="currentTab">
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.1.0 - January 2025</h3>
                            <span class="release-date">Released: January 15, 2025</span>
                        </div>
                        <div class="release-body">
                            <h4>New Features</h4>
                            <ul>
                                <li>Enhanced dashboard customization options</li>
                                <li>Improved search functionality across all modules</li>
                                <li>New month-end processing workflow</li>
                                <li>Advanced reporting capabilities</li>
                            </ul>
                            <h4>Improvements</h4>
                            <ul>
                                <li>Faster loading times for inventory management</li>
                                <li>Better mobile responsiveness</li>
                                <li>Enhanced security features</li>
                            </ul>
                            <h4>Bug Fixes</h4>
                            <ul>
                                <li>Fixed issue with customer data export</li>
                                <li>Resolved branch switching delays</li>
                                <li>Corrected sales report calculations</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="tab-content" id="previousTab">
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.0.5 - December 2024</h3>
                            <span class="release-date">Released: December 20, 2024</span>
                        </div>
                        <div class="release-body">
                            <h4>Bug Fixes</h4>
                            <ul>
                                <li>Fixed critical security vulnerability</li>
                                <li>Resolved database connection issues</li>
                                <li>Improved error handling</li>
                            </ul>
                        </div>
                    </div>
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.0.0 - November 2024</h3>
                            <span class="release-date">Released: November 15, 2024</span>
                        </div>
                        <div class="release-body">
                            <h4>Major Release</h4>
                            <ul>
                                <li>Complete UI redesign</li>
                                <li>New dashboard architecture</li>
                                <li>Enhanced user management</li>
                                <li>Improved performance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Customization Modal -->
    <div class="modal-overlay" id="customizeModal" role="dialog" aria-labelledby="customizeTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="customizeTitle">Customize Dashboard</h2>
                <button class="modal-close" id="closeCustomize" aria-label="Close customization">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="customize-section">
                    <h3>Layout Options</h3>
                    <div class="layout-options">
                        <div class="layout-option active" data-layout="default">
                            <div class="layout-preview">
                                <div class="preview-grid">
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                </div>
                            </div>
                            <span>Default Grid</span>
                        </div>
                        <div class="layout-option" data-layout="compact">
                            <div class="layout-preview">
                                <div class="preview-grid compact">
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                </div>
                            </div>
                            <span>Compact View</span>
                        </div>
                        <div class="layout-option" data-layout="wide">
                            <div class="layout-preview">
                                <div class="preview-grid wide">
                                    <div class="preview-card wide"></div>
                                    <div class="preview-card wide"></div>
                                </div>
                            </div>
                            <span>Wide Cards</span>
                        </div>
                    </div>
                </div>
                <div class="customize-section">
                    <h3>Widget Visibility</h3>
                    <div class="widget-toggles">
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="vehicles">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Total Vehicles</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="sales">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Monthly Sales</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="customers">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Active Customers</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="branches">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Active Branches</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="activity">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Recent Activity</span>
                        </label>
                    </div>
                </div>
                <div class="customize-section">
                    <h3>Theme Options</h3>
                    <div class="theme-options">
                        <div class="theme-option active" data-theme="default">
                            <div class="theme-preview default"></div>
                            <span>Default</span>
                        </div>
                        <div class="theme-option" data-theme="dark">
                            <div class="theme-preview dark"></div>
                            <span>Dark Mode</span>
                        </div>
                        <div class="theme-option" data-theme="blue">
                            <div class="theme-preview blue"></div>
                            <span>Blue Theme</span>
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" id="resetCustomization">Reset to Default</button>
                    <button class="btn-primary" id="saveCustomization">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Month-End Process Modal -->
    <div class="modal-overlay" id="monthEndModal" role="dialog" aria-labelledby="monthEndTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="monthEndTitle">Month-End Process</h2>
                <button class="modal-close" id="closeMonthEnd" aria-label="Close month-end process">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="month-end-status">
                    <h3>January 2025 Month-End Status</h3>
                    <div class="status-grid">
                        <div class="status-item completed">
                            <span class="material-icons">check_circle</span>
                            <span>Sales Reports Generated</span>
                        </div>
                        <div class="status-item completed">
                            <span class="material-icons">check_circle</span>
                            <span>Inventory Reconciliation</span>
                        </div>
                        <div class="status-item in-progress">
                            <span class="material-icons">schedule</span>
                            <span>Financial Closing</span>
                        </div>
                        <div class="status-item pending">
                            <span class="material-icons">radio_button_unchecked</span>
                            <span>Management Reports</span>
                        </div>
                    </div>
                </div>
                <div class="month-end-actions">
                    <h3>Available Actions</h3>
                    <div class="action-buttons">
                        <button class="action-btn" data-action="generate-reports">
                            <span class="material-icons">assessment</span>
                            Generate Reports
                        </button>
                        <button class="action-btn" data-action="reconcile-inventory">
                            <span class="material-icons">inventory</span>
                            Reconcile Inventory
                        </button>
                        <button class="action-btn" data-action="close-period">
                            <span class="material-icons">lock</span>
                            Close Period
                        </button>
                        <button class="action-btn" data-action="export-data">
                            <span class="material-icons">download</span>
                            Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard JavaScript -->
    <script src="dashboard.js"></script>
</body>
</html>
