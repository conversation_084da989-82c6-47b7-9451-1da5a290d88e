<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prevost | DMS (Dealer Management System)</title>

    <!-- Material-UI CSS -->
    <link href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/@mui/material@5.14.20/umd/material-ui.min.css" rel="stylesheet">

    <!-- Dashboard CSS -->
    <link rel="stylesheet" href="dashboard.css">
</head>
<body>
    <div id="root">
        <!-- Header -->
        <header class="dashboard-header">
            <div class="header-content">
                <div class="header-left">
                    <img src="Prevost.png" alt="Prevost Logo" class="header-logo">
                    <div class="header-title">
                        <h1>DMS</h1>
                    </div>
                </div>

                <!-- Header Center - Advanced Search -->
                <div class="header-center">
                    <div class="advanced-search-container">
                        <div class="search-wrapper">
                            <span class="material-icons search-icon">search</span>
                            <input type="text"
                                   id="globalSearch"
                                   class="advanced-search-input"
                                   placeholder="Search anything..."
                                   aria-label="Global search"
                                   autocomplete="off">
                            <div class="search-filters">
                                <button class="filter-btn active" data-filter="all" title="All">
                                    <span class="material-icons">apps</span>
                                </button>
                                <button class="filter-btn" data-filter="vehicles" title="Vehicles">
                                    <span class="material-icons">directions_bus</span>
                                </button>
                                <button class="filter-btn" data-filter="customers" title="Customers">
                                    <span class="material-icons">people</span>
                                </button>
                                <button class="filter-btn" data-filter="reports" title="Reports">
                                    <span class="material-icons">assessment</span>
                                </button>
                            </div>
                            <button class="search-clear" id="searchClear" aria-label="Clear search">
                                <span class="material-icons">close</span>
                            </button>
                        </div>
                        <div class="search-suggestions" id="searchSuggestions">
                            <!-- Dynamic suggestions will be populated here -->
                        </div>
                    </div>
                </div>

                <div class="header-right">
                    <!-- Branch Selection -->
                    <div class="header-dropdown branch-selector" data-tooltip="Switch Branch">
                        <button class="icon-button" id="branchSelector" aria-haspopup="true" aria-expanded="false">
                            <span class="material-icons">business</span>
                        </button>
                        <div class="dropdown-menu" id="branchMenu" role="menu">
                            <div class="dropdown-item active" role="menuitem" data-branch="main">
                                <span class="material-icons">business</span>
                                Main Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="north">
                                <span class="material-icons">business</span>
                                North Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="south">
                                <span class="material-icons">business</span>
                                South Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="east">
                                <span class="material-icons">business</span>
                                East Branch
                            </div>
                            <div class="dropdown-item" role="menuitem" data-branch="west">
                                <span class="material-icons">business</span>
                                West Branch
                            </div>
                        </div>
                    </div>

                    <!-- Release Notes -->
                    <button class="icon-button" id="releaseNotesButton" data-tooltip="Release Notes">
                        <span class="material-icons">new_releases</span>
                        <span class="notification-badge" id="releaseNotesBadge">2</span>
                    </button>

                    <!-- Dashboard Customization -->
                    <button class="icon-button" id="customizeButton" data-tooltip="Customize Dashboard">
                        <span class="material-icons">tune</span>
                    </button>

                    <!-- Month-end Process -->
                    <button class="icon-button month-end-button" id="monthEndButton" data-tooltip="Month-End Process">
                        <span class="material-icons">event_note</span>
                    </button>

                    <!-- Admin Profile -->
                    <div class="header-dropdown admin-dropdown" data-tooltip="Admin Settings">
                        <button class="admin-profile-button" id="adminProfileButton" aria-haspopup="true" aria-expanded="false">
                            <div class="admin-info">
                                <span class="admin-name" id="userName">Administrator</span>
                                <span class="admin-role" id="userRole">Admin</span>
                            </div>
                            <span class="material-icons dropdown-arrow">expand_more</span>
                        </button>
                        <div class="dropdown-menu admin-menu" id="adminMenu" role="menu">
                            <div class="dropdown-item" role="menuitem" data-action="profile">
                                <span class="material-icons">person</span>
                                Profile Settings
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="preferences">
                                <span class="material-icons">settings</span>
                                Preferences
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="security">
                                <span class="material-icons">security</span>
                                Security Settings
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="notifications">
                                <span class="material-icons">notifications</span>
                                Notifications
                            </div>
                            <div class="dropdown-separator"></div>
                            <div class="dropdown-item" role="menuitem" data-action="help">
                                <span class="material-icons">help</span>
                                Help & Support
                            </div>
                            <div class="dropdown-item" role="menuitem" data-action="about">
                                <span class="material-icons">info</span>
                                About DMS
                            </div>
                        </div>
                    </div>

                    <!-- Logout Button -->
                    <button class="icon-button logout-button" id="logoutButton" data-tooltip="Logout">
                        <span class="material-icons">logout</span>
                    </button>
                </div>
            </div>
        </header>

        <!-- Main Content -->
        <main class="dashboard-main">
            <!-- Navigation Sidebar -->
            <nav class="dashboard-nav">
                <div class="nav-section">
                    <h3 class="nav-title">Dashboard</h3>
                    <ul class="nav-list">
                        <li class="nav-item active">
                            <a href="#overview" class="nav-link">
                                <span class="material-icons">dashboard</span>
                                Overview
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#inventory" class="nav-link">
                                <span class="material-icons">directions_bus</span>
                                Inventory
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#sales" class="nav-link">
                                <span class="material-icons">trending_up</span>
                                Sales
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#customers" class="nav-link">
                                <span class="material-icons">people</span>
                                Customers
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h3 class="nav-title">Management</h3>
                    <ul class="nav-list">
                        <li class="nav-item">
                            <a href="#branches" class="nav-link">
                                <span class="material-icons">business</span>
                                Branches
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#users" class="nav-link">
                                <span class="material-icons">admin_panel_settings</span>
                                Users
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#reports" class="nav-link">
                                <span class="material-icons">assessment</span>
                                Reports
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#settings" class="nav-link">
                                <span class="material-icons">settings</span>
                                Settings
                            </a>
                        </li>
                    </ul>
                </div>
            </nav>

            <!-- Content Area -->
            <div class="dashboard-content">
                <div class="content-header">
                    <h2 class="page-title">Dashboard Overview</h2>
                    <div class="breadcrumb">
                        <span>Home</span>
                        <span class="material-icons">chevron_right</span>
                        <span>Dashboard</span>
                    </div>
                </div>

                <!-- Dashboard Cards -->
                <div class="dashboard-cards">
                    <!-- Primary Business Metrics -->
                    <div class="dashboard-card primary-card">
                        <div class="card-icon">
                            <span class="material-icons">request_quote</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Quotation Summary</h3>
                            <p class="card-value">1,247</p>
                            <p class="card-metric">Quotation Conversion Ratio <span class="metric-value">68.5%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-icon">
                            <span class="material-icons">build</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Work Order Summary</h3>
                            <p class="card-value">856</p>
                            <p class="card-metric">Active Work Orders <span class="metric-value">142</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-icon">
                            <span class="material-icons">verified_user</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Warranty Summary</h3>
                            <p class="card-value">324</p>
                            <p class="card-metric">Claimed Workorders <span class="metric-value">15.2%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-icon">
                            <span class="material-icons">receipt</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Invoice Summary</h3>
                            <p class="card-value">$2.8M</p>
                            <p class="card-metric">Invoiced Work Orders <span class="metric-value">92.3%</span></p>
                        </div>
                    </div>

                    <div class="dashboard-card primary-card">
                        <div class="card-icon">
                            <span class="material-icons">receipt_long</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">Internal Invoice Summary</h3>
                            <p class="card-value">$485K</p>
                            <p class="card-metric">Internal Invoiced Workorders <span class="metric-value">78.9%</span></p>
                        </div>
                    </div>

                    <!-- Secondary Metrics -->
                    <div class="dashboard-card secondary-card">
                        <div class="card-icon secondary">
                            <span class="material-icons">analytics</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">TAMS</h3>
                            <p class="card-value">94.7%</p>
                        </div>
                    </div>

                    <div class="dashboard-card secondary-card">
                        <div class="card-icon secondary">
                            <span class="material-icons">trending_down</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">TAMS DEVIATION</h3>
                            <p class="card-value">-2.3%</p>
                        </div>
                    </div>

                    <!-- SAP Interface Summary Cards -->
                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">receipt</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Invoice</p>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Synchronized</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">inventory</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Parts Issues</p>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Synchronized</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">keyboard_return</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Parts Returns</p>
                            <div class="sap-status">
                                <span class="status-indicator warning"></span>
                                <span class="status-text">Pending</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">category</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Parts Master</p>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Synchronized</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">directions_bus</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Vehicle Master</p>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Synchronized</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">people</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Customer Master</p>
                            <div class="sap-status">
                                <span class="status-indicator error"></span>
                                <span class="status-text">Error</span>
                            </div>
                        </div>
                    </div>

                    <div class="dashboard-card sap-card">
                        <div class="card-icon sap">
                            <span class="material-icons">security</span>
                        </div>
                        <div class="card-content">
                            <h3 class="card-title">SAP Interface Summary</h3>
                            <p class="card-subtitle">Warranty</p>
                            <div class="sap-status">
                                <span class="status-indicator success"></span>
                                <span class="status-text">Synchronized</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="dashboard-section">
                    <h3 class="section-title">Recent Activity</h3>
                    <div class="activity-list">
                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">add_circle</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New vehicle added to inventory: Prevost H3-45</p>
                                <p class="activity-time">2 hours ago</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">person_add</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">New customer registered: Transit Solutions Inc.</p>
                                <p class="activity-time">4 hours ago</p>
                            </div>
                        </div>

                        <div class="activity-item">
                            <div class="activity-icon">
                                <span class="material-icons">sell</span>
                            </div>
                            <div class="activity-content">
                                <p class="activity-text">Sale completed: Prevost X3-45 to Metro Transit</p>
                                <p class="activity-time">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Footer -->
        <footer class="dashboard-footer">
            <div class="footer-content">
                <p>&copy; 2025 Prevost. All rights reserved.</p>
                <div class="footer-links">
                    <a href="#" class="footer-link">Privacy Policy</a>
                    <span class="footer-separator">|</span>
                    <a href="#" class="footer-link">Terms of Service</a>
                    <span class="footer-separator">|</span>
                    <a href="#" class="footer-link">Support</a>
                </div>
            </div>
        </footer>
    </div>

    <!-- Release Notes Modal -->
    <div class="modal-overlay" id="releaseNotesModal" role="dialog" aria-labelledby="releaseNotesTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="releaseNotesTitle">Release Notes</h2>
                <button class="modal-close" id="closeReleaseNotes" aria-label="Close release notes">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="release-notes-tabs">
                    <button class="tab-button active" data-tab="current">Current Release</button>
                    <button class="tab-button" data-tab="previous">Previous Releases</button>
                </div>
                <div class="tab-content active" id="currentTab">
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.1.0 - January 2025</h3>
                            <span class="release-date">Released: January 15, 2025</span>
                        </div>
                        <div class="release-body">
                            <h4>New Features</h4>
                            <ul>
                                <li>Enhanced dashboard customization options</li>
                                <li>Improved search functionality across all modules</li>
                                <li>New month-end processing workflow</li>
                                <li>Advanced reporting capabilities</li>
                            </ul>
                            <h4>Improvements</h4>
                            <ul>
                                <li>Faster loading times for inventory management</li>
                                <li>Better mobile responsiveness</li>
                                <li>Enhanced security features</li>
                            </ul>
                            <h4>Bug Fixes</h4>
                            <ul>
                                <li>Fixed issue with customer data export</li>
                                <li>Resolved branch switching delays</li>
                                <li>Corrected sales report calculations</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="tab-content" id="previousTab">
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.0.5 - December 2024</h3>
                            <span class="release-date">Released: December 20, 2024</span>
                        </div>
                        <div class="release-body">
                            <h4>Bug Fixes</h4>
                            <ul>
                                <li>Fixed critical security vulnerability</li>
                                <li>Resolved database connection issues</li>
                                <li>Improved error handling</li>
                            </ul>
                        </div>
                    </div>
                    <div class="release-note">
                        <div class="release-header">
                            <h3>Version 2.0.0 - November 2024</h3>
                            <span class="release-date">Released: November 15, 2024</span>
                        </div>
                        <div class="release-body">
                            <h4>Major Release</h4>
                            <ul>
                                <li>Complete UI redesign</li>
                                <li>New dashboard architecture</li>
                                <li>Enhanced user management</li>
                                <li>Improved performance</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard Customization Modal -->
    <div class="modal-overlay" id="customizeModal" role="dialog" aria-labelledby="customizeTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="customizeTitle">Customize Dashboard</h2>
                <button class="modal-close" id="closeCustomize" aria-label="Close customization">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="customize-section">
                    <h3>Layout Options</h3>
                    <div class="layout-options">
                        <div class="layout-option active" data-layout="default">
                            <div class="layout-preview">
                                <div class="preview-grid">
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                    <div class="preview-card"></div>
                                </div>
                            </div>
                            <span>Default Grid</span>
                        </div>
                        <div class="layout-option" data-layout="compact">
                            <div class="layout-preview">
                                <div class="preview-grid compact">
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                    <div class="preview-card small"></div>
                                </div>
                            </div>
                            <span>Compact View</span>
                        </div>
                        <div class="layout-option" data-layout="wide">
                            <div class="layout-preview">
                                <div class="preview-grid wide">
                                    <div class="preview-card wide"></div>
                                    <div class="preview-card wide"></div>
                                </div>
                            </div>
                            <span>Wide Cards</span>
                        </div>
                    </div>
                </div>
                <div class="customize-section">
                    <h3>Widget Visibility</h3>
                    <div class="widget-toggles">
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="vehicles">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Total Vehicles</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="sales">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Monthly Sales</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="customers">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Active Customers</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="branches">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Active Branches</span>
                        </label>
                        <label class="widget-toggle">
                            <input type="checkbox" checked data-widget="activity">
                            <span class="toggle-slider"></span>
                            <span class="toggle-label">Recent Activity</span>
                        </label>
                    </div>
                </div>
                <div class="customize-section">
                    <h3>Theme Options</h3>
                    <div class="theme-options">
                        <div class="theme-option active" data-theme="default">
                            <div class="theme-preview default"></div>
                            <span>Default</span>
                        </div>
                        <div class="theme-option" data-theme="dark">
                            <div class="theme-preview dark"></div>
                            <span>Dark Mode</span>
                        </div>
                        <div class="theme-option" data-theme="blue">
                            <div class="theme-preview blue"></div>
                            <span>Blue Theme</span>
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button class="btn-secondary" id="resetCustomization">Reset to Default</button>
                    <button class="btn-primary" id="saveCustomization">Save Changes</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Month-End Process Modal -->
    <div class="modal-overlay" id="monthEndModal" role="dialog" aria-labelledby="monthEndTitle" aria-hidden="true">
        <div class="modal-container">
            <div class="modal-header">
                <h2 id="monthEndTitle">Month-End Process</h2>
                <button class="modal-close" id="closeMonthEnd" aria-label="Close month-end process">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <div class="modal-content">
                <div class="month-end-status">
                    <h3>January 2025 Month-End Status</h3>
                    <div class="status-grid">
                        <div class="status-item completed">
                            <span class="material-icons">check_circle</span>
                            <span>Sales Reports Generated</span>
                        </div>
                        <div class="status-item completed">
                            <span class="material-icons">check_circle</span>
                            <span>Inventory Reconciliation</span>
                        </div>
                        <div class="status-item in-progress">
                            <span class="material-icons">schedule</span>
                            <span>Financial Closing</span>
                        </div>
                        <div class="status-item pending">
                            <span class="material-icons">radio_button_unchecked</span>
                            <span>Management Reports</span>
                        </div>
                    </div>
                </div>
                <div class="month-end-actions">
                    <h3>Available Actions</h3>
                    <div class="action-buttons">
                        <button class="action-btn" data-action="generate-reports">
                            <span class="material-icons">assessment</span>
                            Generate Reports
                        </button>
                        <button class="action-btn" data-action="reconcile-inventory">
                            <span class="material-icons">inventory</span>
                            Reconcile Inventory
                        </button>
                        <button class="action-btn" data-action="close-period">
                            <span class="material-icons">lock</span>
                            Close Period
                        </button>
                        <button class="action-btn" data-action="export-data">
                            <span class="material-icons">download</span>
                            Export Data
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Dashboard JavaScript -->
    <script src="dashboard.js"></script>
</body>
</html>
