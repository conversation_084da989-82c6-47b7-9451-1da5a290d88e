/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cambria', serif;
    background-color: #ffffff;
    color: #2c2c2c;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

#root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.dashboard-header {
    background-color: #000000;
    color: #ffffff;
    padding: 1rem 2rem;
    border-bottom: 1px solid #333333;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-logo {
    height: 40px;
    width: auto;
}

.header-title h1 {
    font-family: 'Cambria', serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-subtitle {
    font-size: 0.875rem;
    color: #cccccc;
    font-weight: 400;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: #cccccc;
}

.logout-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: transparent;
    border: 1px solid #666666;
    color: #ffffff;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    cursor: pointer;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    transition: all 0.3s ease;
}

.logout-button:hover {
    background-color: #333333;
    border-color: #999999;
}

/* Main Content */
.dashboard-main {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Navigation Sidebar */
.dashboard-nav {
    width: 250px;
    background-color: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

.nav-section {
    margin-bottom: 2rem;
    padding: 0 1.5rem;
}

.nav-title {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    font-weight: 600;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #2c2c2c;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.nav-link:hover {
    background-color: #e9ecef;
    color: #000000;
}

.nav-item.active .nav-link {
    background-color: #2c2c2c;
    color: #ffffff;
}

.nav-link .material-icons {
    font-size: 20px;
}

/* Content Area */
.dashboard-content {
    flex: 1;
    padding: 2rem;
    background-color: #ffffff;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.page-title {
    font-family: 'Cambria', serif;
    font-size: 2rem;
    font-weight: 700;
    color: #2c2c2c;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666666;
    font-size: 0.875rem;
}

.breadcrumb .material-icons {
    font-size: 16px;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-icon {
    background-color: #2c2c2c;
    color: #ffffff;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon .material-icons {
    font-size: 28px;
}

.card-content {
    flex: 1;
}

.card-title {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #666666;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.card-value {
    font-family: 'Cambria', serif;
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c2c2c;
    margin-bottom: 0.25rem;
}

.card-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.card-change.positive {
    color: #2e7d32;
}

.card-change.negative {
    color: #d32f2f;
}

.card-change.neutral {
    color: #666666;
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 2rem;
}

.section-title {
    font-family: 'Cambria', serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

/* Activity List */
.activity-list {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-icon {
    background-color: #f0f0f0;
    color: #2c2c2c;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-icon .material-icons {
    font-size: 20px;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #666666;
}

/* Footer */
.dashboard-footer {
    background-color: #000000;
    color: #ffffff;
    padding: 1.5rem 2rem;
    border-top: 1px solid #333333;
    margin-top: auto;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
}

.footer-content p {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.footer-link {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #ffffff;
}

.footer-separator {
    color: #666666;
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-header {
        padding: 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 1rem;
        align-items: flex-start;
    }

    .header-right {
        width: 100%;
        justify-content: space-between;
    }

    .dashboard-main {
        flex-direction: column;
    }

    .dashboard-nav {
        width: 100%;
        min-height: auto;
        order: 2;
    }

    .dashboard-content {
        order: 1;
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-card {
        padding: 1rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.25rem;
    }

    .footer-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .header-logo {
        height: 30px;
    }

    .header-title h1 {
        font-size: 1.2rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .card-value {
        font-size: 1.5rem;
    }

    .nav-section {
        padding: 0 1rem;
    }
}
