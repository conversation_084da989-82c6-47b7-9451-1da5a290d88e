/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cambria', serif;
    background-color: #ffffff;
    color: #2c2c2c;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

#root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Header */
.dashboard-header {
    background-color: #000000;
    color: #ffffff;
    padding: 1rem 2rem;
    border-bottom: 1px solid #333333;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1400px;
    margin: 0 auto;
    gap: 2rem;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-logo {
    height: 40px;
    width: auto;
}

.header-title h1 {
    font-family: 'Cambria', serif;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
}

.header-subtitle {
    font-size: 0.875rem;
    color: #cccccc;
    font-weight: 400;
}

/* Header Center - Advanced Search */
.header-center {
    flex: 1;
    display: flex;
    justify-content: center;
    max-width: 600px;
}

.advanced-search-container {
    position: relative;
    width: 100%;
    max-width: 500px;
}

.search-wrapper {
    position: relative;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 30px;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.search-wrapper:focus-within {
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 4px 25px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(255, 255, 255, 0.1);
    transform: translateY(-1px);
}

.advanced-search-input {
    width: 100%;
    padding: 0.75rem 3rem 0.75rem 3rem;
    background: transparent;
    border: none;
    color: #ffffff;
    font-family: 'Cambria', serif;
    font-size: 0.95rem;
    font-weight: 400;
}

.advanced-search-input::placeholder {
    color: rgba(255, 255, 255, 0.7);
    font-style: italic;
}

.advanced-search-input:focus {
    outline: none;
}

.search-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(255, 255, 255, 0.8);
    font-size: 20px;
    pointer-events: none;
    z-index: 2;
}

.search-filters {
    position: absolute;
    right: 3rem;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 0.25rem;
    z-index: 2;
}

.filter-btn {
    width: 28px;
    height: 28px;
    border: none;
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.6);
    border-radius: 50%;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    font-size: 16px;
}

.filter-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.9);
    transform: scale(1.1);
}

.filter-btn.active {
    background: rgba(255, 255, 255, 0.3);
    color: #ffffff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.search-clear {
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: rgba(255, 255, 255, 0.6);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    display: none;
    transition: all 0.3s ease;
    z-index: 2;
}

.search-clear:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
}

.search-clear.visible {
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-suggestions {
    position: absolute;
    top: calc(100% + 8px);
    left: 0;
    right: 0;
    background: #ffffff;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.search-suggestions.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.suggestion-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0.75rem;
    transition: background-color 0.3s ease;
}

.suggestion-item:hover,
.suggestion-item.selected {
    background-color: #f8f9fa;
}

.suggestion-item:last-child {
    border-bottom: none;
}

.suggestion-icon {
    color: #666666;
    font-size: 18px;
}

.suggestion-text {
    flex: 1;
    color: #2c2c2c;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.suggestion-category {
    font-size: 0.8rem;
    color: #999999;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex-shrink: 0;
}

/* Icon Buttons */
.icon-button {
    position: relative;
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.icon-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.icon-button .material-icons {
    font-size: 20px;
}

.month-end-button {
    background-color: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.4);
}

.month-end-button:hover {
    background-color: rgba(76, 175, 80, 0.3);
    border-color: rgba(76, 175, 80, 0.5);
}

.logout-button {
    background-color: rgba(244, 67, 54, 0.1);
    border-color: rgba(244, 67, 54, 0.3);
}

.logout-button:hover {
    background-color: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.4);
}

.notification-badge {
    position: absolute;
    top: -6px;
    right: -6px;
    background-color: #f44336;
    color: #ffffff;
    font-size: 0.7rem;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    text-align: center;
    line-height: 1;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Admin Profile Button */
.admin-profile-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
    min-width: 120px;
}

.admin-profile-button:hover {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.admin-info {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    flex: 1;
}

.admin-name {
    font-weight: 600;
    font-size: 0.85rem;
    line-height: 1.2;
}

.admin-role {
    font-size: 0.75rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.2;
}

/* Tooltips */
[data-tooltip] {
    position: relative;
}

[data-tooltip]:hover::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: calc(100% + 8px);
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: #ffffff;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.8rem;
    font-family: 'Cambria', serif;
    white-space: nowrap;
    z-index: 2000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

[data-tooltip]:hover::after {
    content: '';
    position: absolute;
    bottom: calc(100% + 2px);
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 2000;
    opacity: 0;
    animation: tooltipFadeIn 0.3s ease forwards;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Header Dropdowns */
.header-dropdown {
    position: relative;
}

.dropdown-arrow {
    transition: transform 0.3s ease;
    font-size: 16px;
}

.admin-profile-button.active .dropdown-arrow,
.icon-button.active .dropdown-arrow {
    transform: rotate(180deg);
}

.icon-button.active,
.admin-profile-button.active {
    background-color: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
}

.dropdown-menu {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all 0.3s ease;
}

.dropdown-menu.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #2c2c2c;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item.active {
    background-color: #e3f2fd;
    color: #1976d2;
}

.dropdown-item .material-icons {
    font-size: 18px;
}

.dropdown-separator {
    height: 1px;
    background-color: #e0e0e0;
    margin: 0.5rem 0;
}

.logout-item {
    color: #d32f2f;
}

.logout-item:hover {
    background-color: #ffebee;
}

/* User Profile in Admin Dropdown */
.user-profile {
    padding: 0.5rem 1rem;
}

.user-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    text-align: right;
}

.user-name {
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    font-size: 0.8rem;
    color: #cccccc;
}

.user-avatar {
    margin-left: 0.5rem;
}

.user-avatar .material-icons {
    font-size: 24px;
}

/* Branch Selector Specific */
.branch-selector .dropdown-menu {
    left: 0;
    right: auto;
}

/* Main Content */
.dashboard-main {
    display: flex;
    flex: 1;
    max-width: 1400px;
    margin: 0 auto;
    width: 100%;
}

/* Navigation Sidebar */
.dashboard-nav {
    width: 250px;
    background-color: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    padding: 2rem 0;
    min-height: calc(100vh - 80px);
}

.nav-section {
    margin-bottom: 2rem;
    padding: 0 1.5rem;
}

.nav-title {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    font-weight: 600;
    color: #666666;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-bottom: 1rem;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.5rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: #2c2c2c;
    text-decoration: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.nav-link:hover {
    background-color: #e9ecef;
    color: #000000;
}

.nav-item.active .nav-link {
    background-color: #2c2c2c;
    color: #ffffff;
}

.nav-link .material-icons {
    font-size: 20px;
}

/* Content Area */
.dashboard-content {
    flex: 1;
    padding: 2rem;
    background-color: #ffffff;
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e0e0e0;
}

.page-title {
    font-family: 'Cambria', serif;
    font-size: 2rem;
    font-weight: 700;
    color: #2c2c2c;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #666666;
    font-size: 0.875rem;
}

.breadcrumb .material-icons {
    font-size: 16px;
}

/* Dashboard Cards */
.dashboard-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.dashboard-card {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-icon {
    background-color: #2c2c2c;
    color: #ffffff;
    width: 60px;
    height: 60px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-icon .material-icons {
    font-size: 28px;
}

.card-content {
    flex: 1;
}

.card-title {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #666666;
    margin-bottom: 0.5rem;
    font-weight: 500;
}

.card-value {
    font-family: 'Cambria', serif;
    font-size: 1.75rem;
    font-weight: 700;
    color: #2c2c2c;
    margin-bottom: 0.25rem;
}

.card-change {
    font-size: 0.8rem;
    font-weight: 500;
}

.card-change.positive {
    color: #2e7d32;
}

.card-change.negative {
    color: #d32f2f;
}

.card-change.neutral {
    color: #666666;
}

/* Dashboard Sections */
.dashboard-section {
    margin-bottom: 2rem;
}

.section-title {
    font-family: 'Cambria', serif;
    font-size: 1.25rem;
    font-weight: 600;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

/* Activity List */
.activity-list {
    background: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
}

.activity-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background-color: #f8f9fa;
}

.activity-icon {
    background-color: #f0f0f0;
    color: #2c2c2c;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-icon .material-icons {
    font-size: 20px;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.8rem;
    color: #666666;
}

/* Footer */
.dashboard-footer {
    background-color: #000000;
    color: #ffffff;
    padding: 1.5rem 2rem;
    border-top: 1px solid #333333;
    margin-top: auto;
}

.footer-content {
    max-width: 1400px;
    margin: 0 auto;
    text-align: center;
}

.footer-content p {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    margin-bottom: 0.5rem;
}

.footer-links {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.footer-link {
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #cccccc;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-link:hover {
    color: #ffffff;
}

.footer-separator {
    color: #666666;
    font-size: 0.875rem;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.show {
    opacity: 1;
    visibility: visible;
}

.modal-container {
    background-color: #ffffff;
    border-radius: 12px;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
    max-width: 90vw;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.show .modal-container {
    transform: scale(1);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: #f8f9fa;
}

.modal-header h2 {
    font-family: 'Cambria', serif;
    font-size: 1.5rem;
    font-weight: 600;
    color: #2c2c2c;
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background-color: #e0e0e0;
    color: #2c2c2c;
}

.modal-content {
    padding: 2rem;
    max-height: 70vh;
    overflow-y: auto;
}

/* Release Notes Modal */
.release-notes-tabs {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    border-bottom: 1px solid #e0e0e0;
}

.tab-button {
    background: none;
    border: none;
    padding: 1rem 1.5rem;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #666666;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button.active {
    color: #2c2c2c;
    border-bottom-color: #2c2c2c;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.release-note {
    margin-bottom: 2rem;
    padding: 1.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
}

.release-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.release-header h3 {
    font-family: 'Cambria', serif;
    font-size: 1.2rem;
    color: #2c2c2c;
    margin: 0;
}

.release-date {
    font-size: 0.875rem;
    color: #666666;
}

.release-body h4 {
    font-family: 'Cambria', serif;
    font-size: 1rem;
    color: #2c2c2c;
    margin: 1rem 0 0.5rem 0;
}

.release-body ul {
    margin: 0 0 1rem 1.5rem;
    color: #555555;
}

.release-body li {
    margin-bottom: 0.25rem;
}

/* Customization Modal */
.customize-section {
    margin-bottom: 2rem;
}

.customize-section h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1rem;
}

.layout-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 1rem;
}

.layout-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.layout-option:hover {
    border-color: #cccccc;
}

.layout-option.active {
    border-color: #2c2c2c;
    background-color: #f8f9fa;
}

.layout-preview {
    margin-bottom: 0.5rem;
}

.preview-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 2px;
    height: 60px;
    background-color: #f0f0f0;
    border-radius: 4px;
    padding: 4px;
}

.preview-grid.compact {
    grid-template-columns: 1fr 1fr 1fr;
}

.preview-grid.wide {
    grid-template-columns: 1fr;
}

.preview-card {
    background-color: #2c2c2c;
    border-radius: 2px;
}

.preview-card.small {
    height: 15px;
}

.preview-card.wide {
    height: 25px;
}

.widget-toggles {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.widget-toggle {
    display: flex;
    align-items: center;
    gap: 1rem;
    cursor: pointer;
}

.toggle-slider {
    position: relative;
    width: 50px;
    height: 24px;
    background-color: #cccccc;
    border-radius: 12px;
    transition: background-color 0.3s ease;
}

.toggle-slider::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background-color: #ffffff;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.widget-toggle input[type="checkbox"] {
    display: none;
}

.widget-toggle input[type="checkbox"]:checked + .toggle-slider {
    background-color: #2c2c2c;
}

.widget-toggle input[type="checkbox"]:checked + .toggle-slider::before {
    transform: translateX(26px);
}

.toggle-label {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    color: #2c2c2c;
}

.theme-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 1rem;
}

.theme-option {
    text-align: center;
    cursor: pointer;
    padding: 1rem;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.theme-option:hover {
    border-color: #cccccc;
}

.theme-option.active {
    border-color: #2c2c2c;
    background-color: #f8f9fa;
}

.theme-preview {
    width: 60px;
    height: 40px;
    border-radius: 4px;
    margin: 0 auto 0.5rem;
}

.theme-preview.default {
    background: linear-gradient(135deg, #ffffff 50%, #f8f9fa 50%);
    border: 1px solid #e0e0e0;
}

.theme-preview.dark {
    background: linear-gradient(135deg, #2c2c2c 50%, #000000 50%);
}

.theme-preview.blue {
    background: linear-gradient(135deg, #1976d2 50%, #0d47a1 50%);
}

.modal-actions {
    display: flex;
    justify-content: flex-end;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1rem;
    border-top: 1px solid #e0e0e0;
}

.btn-primary, .btn-secondary {
    padding: 0.75rem 1.5rem;
    border-radius: 6px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn-primary {
    background-color: #2c2c2c;
    color: #ffffff;
}

.btn-primary:hover {
    background-color: #1a1a1a;
}

.btn-secondary {
    background-color: transparent;
    color: #666666;
    border: 1px solid #cccccc;
}

.btn-secondary:hover {
    background-color: #f8f9fa;
    border-color: #999999;
}

/* Month-End Modal */
.month-end-status h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1.5rem;
}

.status-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem;
    border-radius: 8px;
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
}

.status-item.completed {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.status-item.in-progress {
    background-color: #fff3e0;
    color: #f57c00;
}

.status-item.pending {
    background-color: #f5f5f5;
    color: #666666;
}

.month-end-actions h3 {
    font-family: 'Cambria', serif;
    font-size: 1.1rem;
    color: #2c2c2c;
    margin-bottom: 1.5rem;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 1rem;
}

.action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    padding: 1.5rem 1rem;
    background-color: #f8f9fa;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: 'Cambria', serif;
    font-size: 0.875rem;
    color: #2c2c2c;
}

.action-btn:hover {
    background-color: #e9ecef;
    border-color: #cccccc;
    transform: translateY(-2px);
}

.action-btn .material-icons {
    font-size: 24px;
    color: #666666;
}

/* Notification System */
.notification-container {
    position: fixed;
    top: 80px;
    right: 20px;
    z-index: 3000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.notification {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    min-width: 300px;
    max-width: 400px;
    animation: slideIn 0.3s ease;
}

.notification-info {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    color: #1565c0;
}

.notification-success {
    background-color: #e8f5e8;
    border-left: 4px solid #4caf50;
    color: #2e7d32;
}

.notification-warning {
    background-color: #fff3e0;
    border-left: 4px solid #ff9800;
    color: #f57c00;
}

.notification-error {
    background-color: #ffebee;
    border-left: 4px solid #f44336;
    color: #c62828;
}

.notification-message {
    font-family: 'Cambria', serif;
    font-size: 0.9rem;
    flex: 1;
}

.notification-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 50%;
    transition: background-color 0.3s ease;
}

.notification-close:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Accessibility */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Focus styles for better accessibility */
.dropdown-item:focus,
.header-button:focus,
.dropdown-trigger:focus,
.search-input:focus,
.nav-link:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

.modal-close:focus {
    outline: 2px solid #2196f3;
    outline-offset: 2px;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .header-content {
        gap: 1rem;
    }

    .header-center {
        max-width: 350px;
    }

    .search-filters {
        display: none;
    }

    .advanced-search-input {
        padding-right: 3rem;
    }
}

@media (max-width: 768px) {
    .dashboard-header {
        padding: 0.75rem 1rem;
    }

    .header-content {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .header-left {
        justify-content: center;
    }

    .header-center {
        order: 3;
        max-width: none;
    }

    .header-right {
        order: 2;
        justify-content: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .icon-button {
        width: 36px;
        height: 36px;
    }

    .icon-button .material-icons {
        font-size: 18px;
    }

    .admin-profile-button {
        padding: 0.4rem 0.6rem;
        min-width: 100px;
    }

    .admin-name {
        font-size: 0.8rem;
    }

    .admin-role {
        font-size: 0.7rem;
    }

    .search-wrapper {
        border-radius: 20px;
    }

    .advanced-search-input {
        padding: 0.6rem 2.5rem 0.6rem 2.5rem;
        font-size: 0.9rem;
    }

    .search-icon {
        left: 0.75rem;
        font-size: 18px;
    }

    .search-clear {
        right: 0.6rem;
    }

    .filter-btn {
        width: 24px;
        height: 24px;
        font-size: 14px;
    }

    [data-tooltip]:hover::before,
    [data-tooltip]:hover::after {
        display: none;
    }

    .modal-container {
        max-width: 95vw;
        margin: 1rem;
    }

    .modal-content {
        padding: 1rem;
    }

    .layout-options,
    .theme-options {
        grid-template-columns: repeat(2, 1fr);
    }

    .action-buttons {
        grid-template-columns: 1fr;
    }

    .status-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .header-right {
        gap: 0.25rem;
    }

    .icon-button {
        width: 32px;
        height: 32px;
    }

    .icon-button .material-icons {
        font-size: 16px;
    }

    .admin-profile-button {
        min-width: 80px;
        padding: 0.3rem 0.5rem;
    }

    .admin-name {
        font-size: 0.75rem;
    }

    .admin-role {
        font-size: 0.65rem;
    }

    .notification-badge {
        top: -4px;
        right: -4px;
        font-size: 0.6rem;
        padding: 1px 4px;
    }
}

    .dashboard-main {
        flex-direction: column;
    }

    .dashboard-nav {
        width: 100%;
        min-height: auto;
        order: 2;
    }

    .dashboard-content {
        order: 1;
        padding: 1rem;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .dashboard-cards {
        grid-template-columns: 1fr;
        gap: 1rem;
    }

    .dashboard-card {
        padding: 1rem;
    }

    .footer-links {
        flex-direction: column;
        gap: 0.25rem;
    }

    .footer-separator {
        display: none;
    }
}

@media (max-width: 480px) {
    .header-logo {
        height: 30px;
    }

    .header-title h1 {
        font-size: 1.2rem;
    }

    .page-title {
        font-size: 1.5rem;
    }

    .card-value {
        font-size: 1.5rem;
    }

    .nav-section {
        padding: 0 1rem;
    }
}
