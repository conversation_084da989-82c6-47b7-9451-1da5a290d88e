// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// Initialize dashboard functionality
function initializeDashboard() {
    // Load user information from URL parameters or localStorage
    loadUserInfo();
    
    // Initialize navigation
    initializeNavigation();
    
    // Initialize logout functionality
    initializeLogout();
    
    // Initialize dashboard interactions
    initializeDashboardInteractions();
    
    // Load dashboard data
    loadDashboardData();
}

// Load user information
function loadUserInfo() {
    // Get user info from URL parameters or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const userName = urlParams.get('user') || localStorage.getItem('dashboard_user_name') || 'Administrator';
    const userRole = urlParams.get('role') || localStorage.getItem('dashboard_user_role') || 'Admin';
    
    // Update UI
    document.getElementById('userName').textContent = userName;
    document.getElementById('userRole').textContent = userRole;
    
    // Store in localStorage for future use
    localStorage.setItem('dashboard_user_name', userName);
    localStorage.setItem('dashboard_user_role', userRole);
}

// Initialize navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // Add active class to clicked item
            this.parentElement.classList.add('active');
            
            // Get the target section
            const target = this.getAttribute('href').substring(1);
            
            // Update page content based on navigation
            updatePageContent(target);
        });
    });
}

// Initialize logout functionality
function initializeLogout() {
    const logoutButton = document.getElementById('logoutButton');
    
    logoutButton.addEventListener('click', function() {
        // Confirm logout
        if (confirm('Are you sure you want to logout?')) {
            // Clear stored user data
            localStorage.removeItem('dashboard_user_name');
            localStorage.removeItem('dashboard_user_role');
            localStorage.removeItem('prevost_remembered_credentials');
            
            // Redirect to login page
            window.location.href = 'index.html';
        }
    });
}

// Initialize dashboard interactions
function initializeDashboardInteractions() {
    // Add hover effects and click handlers for dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');
    
    dashboardCards.forEach(card => {
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('.card-title').textContent;
            showCardDetails(cardTitle);
        });
    });
    
    // Add click handlers for activity items
    const activityItems = document.querySelectorAll('.activity-item');
    
    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            const activityText = this.querySelector('.activity-text').textContent;
            showActivityDetails(activityText);
        });
    });
}

// Update page content based on navigation
function updatePageContent(section) {
    const pageTitle = document.querySelector('.page-title');
    const breadcrumb = document.querySelector('.breadcrumb');
    
    // Update page title and breadcrumb based on section
    switch(section) {
        case 'overview':
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
            break;
        case 'inventory':
            pageTitle.textContent = 'Vehicle Inventory';
            updateBreadcrumb(['Home', 'Inventory']);
            break;
        case 'sales':
            pageTitle.textContent = 'Sales Management';
            updateBreadcrumb(['Home', 'Sales']);
            break;
        case 'customers':
            pageTitle.textContent = 'Customer Management';
            updateBreadcrumb(['Home', 'Customers']);
            break;
        case 'branches':
            pageTitle.textContent = 'Branch Management';
            updateBreadcrumb(['Home', 'Management', 'Branches']);
            break;
        case 'users':
            pageTitle.textContent = 'User Management';
            updateBreadcrumb(['Home', 'Management', 'Users']);
            break;
        case 'reports':
            pageTitle.textContent = 'Reports & Analytics';
            updateBreadcrumb(['Home', 'Management', 'Reports']);
            break;
        case 'settings':
            pageTitle.textContent = 'System Settings';
            updateBreadcrumb(['Home', 'Management', 'Settings']);
            break;
        default:
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
    }
    
    // In a real application, you would load different content here
    console.log(`Navigated to: ${section}`);
}

// Update breadcrumb navigation
function updateBreadcrumb(items) {
    const breadcrumb = document.querySelector('.breadcrumb');
    breadcrumb.innerHTML = '';
    
    items.forEach((item, index) => {
        const span = document.createElement('span');
        span.textContent = item;
        breadcrumb.appendChild(span);
        
        if (index < items.length - 1) {
            const icon = document.createElement('span');
            icon.className = 'material-icons';
            icon.textContent = 'chevron_right';
            breadcrumb.appendChild(icon);
        }
    });
}

// Show card details (placeholder functionality)
function showCardDetails(cardTitle) {
    alert(`Viewing details for: ${cardTitle}\n\nIn a real application, this would open a detailed view or modal with more information about ${cardTitle.toLowerCase()}.`);
}

// Show activity details (placeholder functionality)
function showActivityDetails(activityText) {
    alert(`Activity Details:\n\n${activityText}\n\nIn a real application, this would show more detailed information about this activity.`);
}

// Load dashboard data (simulated)
function loadDashboardData() {
    // Simulate loading dashboard data
    console.log('Loading dashboard data...');
    
    // In a real application, you would make API calls here to load:
    // - Vehicle inventory data
    // - Sales statistics
    // - Customer information
    // - Recent activities
    // - Branch status
    
    // For now, we'll just log that data is loaded
    setTimeout(() => {
        console.log('Dashboard data loaded successfully');
        
        // Add a subtle animation to indicate data is loaded
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                
                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    }, 1000);
}

// Utility function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Handle window resize for responsive behavior
window.addEventListener('resize', function() {
    // Adjust layout if needed for responsive design
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        // Mobile-specific adjustments
        document.body.classList.add('mobile-view');
    } else {
        // Desktop-specific adjustments
        document.body.classList.remove('mobile-view');
    }
});

// Initialize responsive behavior on load
window.addEventListener('load', function() {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
        document.body.classList.add('mobile-view');
    }
});

// Keyboard navigation support
document.addEventListener('keydown', function(event) {
    // ESC key to close any open modals or return to overview
    if (event.key === 'Escape') {
        // Return to overview
        const overviewLink = document.querySelector('a[href="#overview"]');
        if (overviewLink) {
            overviewLink.click();
        }
    }
    
    // Ctrl/Cmd + L for logout
    if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
        event.preventDefault();
        document.getElementById('logoutButton').click();
    }
});

// Add accessibility enhancements
function enhanceAccessibility() {
    // Add ARIA labels and roles where needed
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.setAttribute('role', 'button');
        link.setAttribute('tabindex', '0');
    });
    
    // Add keyboard support for nav links
    navLinks.forEach(link => {
        link.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.click();
            }
        });
    });
}

// Initialize accessibility enhancements
enhanceAccessibility();
