// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    initializeDashboard();
});

// Initialize dashboard functionality
function initializeDashboard() {
    // Load user information from URL parameters or localStorage
    loadUserInfo();

    // Initialize navigation
    initializeNavigation();

    // Initialize header functionality
    initializeHeaderComponents();

    // Initialize logout functionality
    initializeLogout();

    // Initialize dashboard interactions
    initializeDashboardInteractions();

    // Load dashboard data
    loadDashboardData();
}

// Load user information
function loadUserInfo() {
    // Get user info from URL parameters or localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const userName = urlParams.get('user') || localStorage.getItem('dashboard_user_name') || 'Administrator';
    const userRole = urlParams.get('role') || localStorage.getItem('dashboard_user_role') || 'Admin';

    // Update UI
    document.getElementById('userName').textContent = userName;
    document.getElementById('userRole').textContent = userRole;

    // Store in localStorage for future use
    localStorage.setItem('dashboard_user_name', userName);
    localStorage.setItem('dashboard_user_role', userRole);
}

// Initialize header components
function initializeHeaderComponents() {
    initializeSearch();
    initializeBranchSelector();
    initializeReleaseNotes();
    initializeCustomization();
    initializeMonthEnd();
    initializeAdminProfile();
}

// Initialize search functionality
function initializeSearch() {
    const searchInput = document.getElementById('globalSearch');
    const searchClear = document.getElementById('searchClear');

    searchInput.addEventListener('input', function() {
        if (this.value.length > 0) {
            searchClear.classList.add('visible');
        } else {
            searchClear.classList.remove('visible');
        }

        // Perform search (placeholder functionality)
        performSearch(this.value);
    });

    searchClear.addEventListener('click', function() {
        searchInput.value = '';
        this.classList.remove('visible');
        searchInput.focus();
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(event) {
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            searchInput.focus();
        }
    });
}

// Initialize branch selector
function initializeBranchSelector() {
    const branchSelector = document.getElementById('branchSelector');
    const branchMenu = document.getElementById('branchMenu');

    branchSelector.addEventListener('click', function() {
        toggleDropdown(this, branchMenu);
    });

    // Handle branch selection
    branchMenu.addEventListener('click', function(e) {
        const branchItem = e.target.closest('.dropdown-item');
        if (branchItem) {
            // Remove active class from all items
            this.querySelectorAll('.dropdown-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to selected item
            branchItem.classList.add('active');

            // Update button text
            const branchText = branchItem.textContent.trim();
            branchSelector.querySelector('.dropdown-text').textContent = branchText;

            // Close dropdown
            closeDropdown(branchSelector, branchMenu);

            // Switch branch (placeholder functionality)
            switchBranch(branchItem.dataset.branch);
        }
    });
}

// Initialize release notes
function initializeReleaseNotes() {
    const releaseNotesButton = document.getElementById('releaseNotesButton');
    const releaseNotesModal = document.getElementById('releaseNotesModal');
    const closeReleaseNotes = document.getElementById('closeReleaseNotes');

    releaseNotesButton.addEventListener('click', function() {
        showModal(releaseNotesModal);
        // Mark release notes as read
        const badge = document.getElementById('releaseNotesBadge');
        if (badge) {
            badge.style.display = 'none';
        }
    });

    closeReleaseNotes.addEventListener('click', function() {
        hideModal(releaseNotesModal);
    });

    // Initialize tabs
    initializeReleaseTabs();
}

// Initialize customization
function initializeCustomization() {
    const customizeButton = document.getElementById('customizeButton');
    const customizeModal = document.getElementById('customizeModal');
    const closeCustomize = document.getElementById('closeCustomize');
    const saveCustomization = document.getElementById('saveCustomization');
    const resetCustomization = document.getElementById('resetCustomization');

    customizeButton.addEventListener('click', function() {
        showModal(customizeModal);
    });

    closeCustomize.addEventListener('click', function() {
        hideModal(customizeModal);
    });

    saveCustomization.addEventListener('click', function() {
        saveCustomizationSettings();
        hideModal(customizeModal);
    });

    resetCustomization.addEventListener('click', function() {
        if (confirm('Are you sure you want to reset all customizations to default?')) {
            resetCustomizationSettings();
        }
    });

    // Initialize customization options
    initializeCustomizationOptions();
}

// Initialize month-end process
function initializeMonthEnd() {
    const monthEndButton = document.getElementById('monthEndButton');
    const monthEndModal = document.getElementById('monthEndModal');
    const closeMonthEnd = document.getElementById('closeMonthEnd');

    monthEndButton.addEventListener('click', function() {
        showModal(monthEndModal);
    });

    closeMonthEnd.addEventListener('click', function() {
        hideModal(monthEndModal);
    });

    // Initialize month-end actions
    initializeMonthEndActions();
}

// Initialize admin profile
function initializeAdminProfile() {
    const adminProfileButton = document.getElementById('adminProfileButton');
    const adminMenu = document.getElementById('adminMenu');

    adminProfileButton.addEventListener('click', function() {
        toggleDropdown(this, adminMenu);
    });

    // Handle admin menu actions
    adminMenu.addEventListener('click', function(e) {
        const menuItem = e.target.closest('.dropdown-item');
        if (menuItem) {
            const action = menuItem.dataset.action;
            closeDropdown(adminProfileButton, adminMenu);
            handleAdminAction(action);
        }
    });
}

// Initialize navigation functionality
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');

    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            // Remove active class from all nav items
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            // Add active class to clicked item
            this.parentElement.classList.add('active');

            // Get the target section
            const target = this.getAttribute('href').substring(1);

            // Update page content based on navigation
            updatePageContent(target);
        });
    });
}

// Dropdown utility functions
function toggleDropdown(trigger, menu) {
    const isOpen = trigger.classList.contains('active');

    // Close all other dropdowns
    document.querySelectorAll('.dropdown-trigger.active').forEach(btn => {
        if (btn !== trigger) {
            btn.classList.remove('active');
            btn.setAttribute('aria-expanded', 'false');
        }
    });

    document.querySelectorAll('.dropdown-menu.show').forEach(dropdown => {
        if (dropdown !== menu) {
            dropdown.classList.remove('show');
        }
    });

    // Toggle current dropdown
    if (isOpen) {
        closeDropdown(trigger, menu);
    } else {
        openDropdown(trigger, menu);
    }
}

function openDropdown(trigger, menu) {
    trigger.classList.add('active');
    trigger.setAttribute('aria-expanded', 'true');
    menu.classList.add('show');
}

function closeDropdown(trigger, menu) {
    trigger.classList.remove('active');
    trigger.setAttribute('aria-expanded', 'false');
    menu.classList.remove('show');
}

// Modal utility functions
function showModal(modal) {
    modal.classList.add('show');
    modal.setAttribute('aria-hidden', 'false');
    document.body.style.overflow = 'hidden';

    // Focus management
    const firstFocusable = modal.querySelector('button, input, select, textarea, [tabindex]:not([tabindex="-1"])');
    if (firstFocusable) {
        firstFocusable.focus();
    }
}

function hideModal(modal) {
    modal.classList.remove('show');
    modal.setAttribute('aria-hidden', 'true');
    document.body.style.overflow = '';
}

// Search functionality
function performSearch(query) {
    if (query.length < 2) return;

    console.log(`Searching for: ${query}`);
    // In a real application, this would make an API call to search
    // across vehicles, customers, reports, etc.

    // Placeholder: Show search results
    // This could open a search results modal or update the main content
}

// Branch switching
function switchBranch(branchId) {
    console.log(`Switching to branch: ${branchId}`);

    // Store selected branch
    localStorage.setItem('selected_branch', branchId);

    // In a real application, this would:
    // 1. Update all data to show branch-specific information
    // 2. Refresh dashboard cards with branch data
    // 3. Update navigation context

    // Show notification
    showNotification(`Switched to ${branchId} branch`, 'success');
}

// Release notes tabs
function initializeReleaseTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // Remove active class from all tabs
            tabButtons.forEach(btn => btn.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // Add active class to clicked tab
            this.classList.add('active');
            document.getElementById(targetTab + 'Tab').classList.add('active');
        });
    });
}

// Customization options
function initializeCustomizationOptions() {
    // Layout options
    const layoutOptions = document.querySelectorAll('.layout-option');
    layoutOptions.forEach(option => {
        option.addEventListener('click', function() {
            layoutOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Theme options
    const themeOptions = document.querySelectorAll('.theme-option');
    themeOptions.forEach(option => {
        option.addEventListener('click', function() {
            themeOptions.forEach(opt => opt.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // Load saved customizations
    loadCustomizationSettings();
}

function saveCustomizationSettings() {
    const settings = {
        layout: document.querySelector('.layout-option.active')?.dataset.layout || 'default',
        theme: document.querySelector('.theme-option.active')?.dataset.theme || 'default',
        widgets: {}
    };

    // Get widget visibility settings
    document.querySelectorAll('.widget-toggle input').forEach(input => {
        settings.widgets[input.dataset.widget] = input.checked;
    });

    localStorage.setItem('dashboard_customization', JSON.stringify(settings));

    // Apply settings
    applyCustomizationSettings(settings);

    showNotification('Dashboard customization saved', 'success');
}

function loadCustomizationSettings() {
    const saved = localStorage.getItem('dashboard_customization');
    if (saved) {
        const settings = JSON.parse(saved);
        applyCustomizationSettings(settings);

        // Update UI to reflect saved settings
        document.querySelector(`[data-layout="${settings.layout}"]`)?.classList.add('active');
        document.querySelector(`[data-theme="${settings.theme}"]`)?.classList.add('active');

        Object.entries(settings.widgets || {}).forEach(([widget, visible]) => {
            const toggle = document.querySelector(`[data-widget="${widget}"]`);
            if (toggle) {
                toggle.checked = visible;
            }
        });
    }
}

function resetCustomizationSettings() {
    localStorage.removeItem('dashboard_customization');

    // Reset UI to defaults
    document.querySelectorAll('.layout-option').forEach(opt => opt.classList.remove('active'));
    document.querySelectorAll('.theme-option').forEach(opt => opt.classList.remove('active'));
    document.querySelector('[data-layout="default"]')?.classList.add('active');
    document.querySelector('[data-theme="default"]')?.classList.add('active');

    document.querySelectorAll('.widget-toggle input').forEach(input => {
        input.checked = true;
    });

    // Apply default settings
    applyCustomizationSettings({
        layout: 'default',
        theme: 'default',
        widgets: {}
    });

    showNotification('Dashboard reset to default settings', 'info');
}

function applyCustomizationSettings(settings) {
    // Apply layout
    document.body.className = document.body.className.replace(/layout-\w+/g, '');
    document.body.classList.add(`layout-${settings.layout}`);

    // Apply theme
    document.body.className = document.body.className.replace(/theme-\w+/g, '');
    document.body.classList.add(`theme-${settings.theme}`);

    // Apply widget visibility
    Object.entries(settings.widgets || {}).forEach(([widget, visible]) => {
        const element = document.querySelector(`[data-widget-id="${widget}"]`);
        if (element) {
            element.style.display = visible ? '' : 'none';
        }
    });
}

// Month-end process functionality
function initializeMonthEndActions() {
    const actionButtons = document.querySelectorAll('.action-btn');

    actionButtons.forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            handleMonthEndAction(action);
        });
    });
}

function handleMonthEndAction(action) {
    switch(action) {
        case 'generate-reports':
            showNotification('Generating month-end reports...', 'info');
            // Simulate report generation
            setTimeout(() => {
                showNotification('Reports generated successfully', 'success');
            }, 2000);
            break;

        case 'reconcile-inventory':
            showNotification('Starting inventory reconciliation...', 'info');
            // Simulate reconciliation process
            setTimeout(() => {
                showNotification('Inventory reconciliation completed', 'success');
            }, 3000);
            break;

        case 'close-period':
            if (confirm('Are you sure you want to close the current period? This action cannot be undone.')) {
                showNotification('Closing period...', 'info');
                setTimeout(() => {
                    showNotification('Period closed successfully', 'success');
                }, 2000);
            }
            break;

        case 'export-data':
            showNotification('Preparing data export...', 'info');
            // Simulate data export
            setTimeout(() => {
                showNotification('Data export ready for download', 'success');
                // In a real application, this would trigger a file download
            }, 1500);
            break;
    }
}

// Admin profile actions
function handleAdminAction(action) {
    switch(action) {
        case 'profile':
            showNotification('Opening profile settings...', 'info');
            // In a real application, this would open a profile settings page/modal
            break;

        case 'preferences':
            showNotification('Opening preferences...', 'info');
            // In a real application, this would open a preferences page/modal
            break;

        case 'security':
            showNotification('Opening security settings...', 'info');
            // In a real application, this would open security settings
            break;

        case 'notifications':
            showNotification('Opening notification settings...', 'info');
            // In a real application, this would open notification preferences
            break;

        case 'help':
            showNotification('Opening help documentation...', 'info');
            // In a real application, this would open help/documentation
            break;

        case 'about':
            showNotification('Opening about information...', 'info');
            // In a real application, this would show version/about info
            break;

        case 'logout':
            handleLogout();
            break;
    }
}

function handleLogout() {
    if (confirm('Are you sure you want to logout?')) {
        // Clear stored user data
        localStorage.removeItem('dashboard_user_name');
        localStorage.removeItem('dashboard_user_role');
        localStorage.removeItem('prevost_remembered_credentials');
        localStorage.removeItem('selected_branch');
        localStorage.removeItem('dashboard_customization');

        // Redirect to login page
        window.location.href = 'index.html';
    }
}

// Notification system
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()" aria-label="Close notification">
            <span class="material-icons">close</span>
        </button>
    `;

    // Add to page
    let container = document.querySelector('.notification-container');
    if (!container) {
        container = document.createElement('div');
        container.className = 'notification-container';
        document.body.appendChild(container);
    }

    container.appendChild(notification);

    // Announce to screen readers
    const liveRegion = document.getElementById('live-region');
    if (liveRegion) {
        liveRegion.textContent = message;
        // Clear after announcement
        setTimeout(() => {
            liveRegion.textContent = '';
        }, 1000);
    }

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Initialize logout functionality
function initializeLogout() {
    // Logout is now handled through the admin profile dropdown
    // But we keep this function for backward compatibility
}

// Initialize dashboard interactions
function initializeDashboardInteractions() {
    // Add hover effects and click handlers for dashboard cards
    const dashboardCards = document.querySelectorAll('.dashboard-card');

    dashboardCards.forEach(card => {
        card.addEventListener('click', function() {
            const cardTitle = this.querySelector('.card-title').textContent;
            showCardDetails(cardTitle);
        });
    });

    // Add click handlers for activity items
    const activityItems = document.querySelectorAll('.activity-item');

    activityItems.forEach(item => {
        item.addEventListener('click', function() {
            const activityText = this.querySelector('.activity-text').textContent;
            showActivityDetails(activityText);
        });
    });
}

// Update page content based on navigation
function updatePageContent(section) {
    const pageTitle = document.querySelector('.page-title');
    const breadcrumb = document.querySelector('.breadcrumb');

    // Update page title and breadcrumb based on section
    switch(section) {
        case 'overview':
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
            break;
        case 'inventory':
            pageTitle.textContent = 'Vehicle Inventory';
            updateBreadcrumb(['Home', 'Inventory']);
            break;
        case 'sales':
            pageTitle.textContent = 'Sales Management';
            updateBreadcrumb(['Home', 'Sales']);
            break;
        case 'customers':
            pageTitle.textContent = 'Customer Management';
            updateBreadcrumb(['Home', 'Customers']);
            break;
        case 'branches':
            pageTitle.textContent = 'Branch Management';
            updateBreadcrumb(['Home', 'Management', 'Branches']);
            break;
        case 'users':
            pageTitle.textContent = 'User Management';
            updateBreadcrumb(['Home', 'Management', 'Users']);
            break;
        case 'reports':
            pageTitle.textContent = 'Reports & Analytics';
            updateBreadcrumb(['Home', 'Management', 'Reports']);
            break;
        case 'settings':
            pageTitle.textContent = 'System Settings';
            updateBreadcrumb(['Home', 'Management', 'Settings']);
            break;
        default:
            pageTitle.textContent = 'Dashboard Overview';
            updateBreadcrumb(['Home', 'Dashboard']);
    }

    // In a real application, you would load different content here
    console.log(`Navigated to: ${section}`);
}

// Update breadcrumb navigation
function updateBreadcrumb(items) {
    const breadcrumb = document.querySelector('.breadcrumb');
    breadcrumb.innerHTML = '';

    items.forEach((item, index) => {
        const span = document.createElement('span');
        span.textContent = item;
        breadcrumb.appendChild(span);

        if (index < items.length - 1) {
            const icon = document.createElement('span');
            icon.className = 'material-icons';
            icon.textContent = 'chevron_right';
            breadcrumb.appendChild(icon);
        }
    });
}

// Show card details (placeholder functionality)
function showCardDetails(cardTitle) {
    alert(`Viewing details for: ${cardTitle}\n\nIn a real application, this would open a detailed view or modal with more information about ${cardTitle.toLowerCase()}.`);
}

// Show activity details (placeholder functionality)
function showActivityDetails(activityText) {
    alert(`Activity Details:\n\n${activityText}\n\nIn a real application, this would show more detailed information about this activity.`);
}

// Load dashboard data (simulated)
function loadDashboardData() {
    // Simulate loading dashboard data
    console.log('Loading dashboard data...');

    // In a real application, you would make API calls here to load:
    // - Vehicle inventory data
    // - Sales statistics
    // - Customer information
    // - Recent activities
    // - Branch status

    // For now, we'll just log that data is loaded
    setTimeout(() => {
        console.log('Dashboard data loaded successfully');

        // Add a subtle animation to indicate data is loaded
        const cards = document.querySelectorAll('.dashboard-card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';

                setTimeout(() => {
                    card.style.transition = 'all 0.5s ease';
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                }, 100);
            }, index * 100);
        });
    }, 1000);
}

// Utility function to format numbers
function formatNumber(num) {
    return new Intl.NumberFormat().format(num);
}

// Utility function to format currency
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Handle window resize for responsive behavior
window.addEventListener('resize', function() {
    // Adjust layout if needed for responsive design
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        // Mobile-specific adjustments
        document.body.classList.add('mobile-view');
    } else {
        // Desktop-specific adjustments
        document.body.classList.remove('mobile-view');
    }
});

// Initialize responsive behavior on load
window.addEventListener('load', function() {
    const isMobile = window.innerWidth <= 768;

    if (isMobile) {
        document.body.classList.add('mobile-view');
    }
});

// Global event listeners
document.addEventListener('click', function(event) {
    // Close dropdowns when clicking outside
    if (!event.target.closest('.header-dropdown')) {
        document.querySelectorAll('.dropdown-trigger.active').forEach(trigger => {
            const menu = trigger.nextElementSibling;
            closeDropdown(trigger, menu);
        });
    }

    // Close modals when clicking on overlay
    if (event.target.classList.contains('modal-overlay')) {
        hideModal(event.target);
    }
});

// Keyboard navigation support
document.addEventListener('keydown', function(event) {
    // ESC key to close modals and dropdowns
    if (event.key === 'Escape') {
        // Close any open modals
        document.querySelectorAll('.modal-overlay.show').forEach(modal => {
            hideModal(modal);
        });

        // Close any open dropdowns
        document.querySelectorAll('.dropdown-trigger.active').forEach(trigger => {
            const menu = trigger.nextElementSibling;
            closeDropdown(trigger, menu);
        });

        // Return to overview if no modals/dropdowns are open
        const overviewLink = document.querySelector('a[href="#overview"]');
        if (overviewLink) {
            overviewLink.click();
        }
    }

    // Ctrl/Cmd + L for logout
    if ((event.ctrlKey || event.metaKey) && event.key === 'l') {
        event.preventDefault();
        handleLogout();
    }

    // Arrow key navigation for dropdowns
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
        const activeDropdown = document.querySelector('.dropdown-menu.show');
        if (activeDropdown) {
            event.preventDefault();
            navigateDropdown(activeDropdown, event.key === 'ArrowDown');
        }
    }

    // Enter key to select dropdown item
    if (event.key === 'Enter') {
        const focusedItem = document.querySelector('.dropdown-item:focus');
        if (focusedItem) {
            focusedItem.click();
        }
    }
});

function navigateDropdown(dropdown, down) {
    const items = dropdown.querySelectorAll('.dropdown-item');
    const currentFocus = dropdown.querySelector('.dropdown-item:focus');
    let index = currentFocus ? Array.from(items).indexOf(currentFocus) : -1;

    if (down) {
        index = index < items.length - 1 ? index + 1 : 0;
    } else {
        index = index > 0 ? index - 1 : items.length - 1;
    }

    items[index].focus();
}

// Add accessibility enhancements
function enhanceAccessibility() {
    // Add ARIA labels and roles where needed
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.setAttribute('role', 'button');
        link.setAttribute('tabindex', '0');
    });

    // Add keyboard support for nav links
    navLinks.forEach(link => {
        link.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.click();
            }
        });
    });

    // Make dropdown items focusable
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        item.setAttribute('tabindex', '0');
        item.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                this.click();
            }
        });
    });

    // Add ARIA labels to buttons
    const headerButtons = document.querySelectorAll('.header-button');
    headerButtons.forEach(button => {
        if (!button.getAttribute('aria-label')) {
            const text = button.querySelector('.button-text')?.textContent ||
                        button.textContent.trim();
            button.setAttribute('aria-label', text);
        }
    });

    // Add live region for notifications
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.className = 'sr-only';
    liveRegion.id = 'live-region';
    document.body.appendChild(liveRegion);
}

// Initialize accessibility enhancements
enhanceAccessibility();
